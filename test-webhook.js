// Test webhook submission to HighLevel
const testData = {
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone": "5091234567",
  "email": "<EMAIL>",
  "address": "",
  "city": "",
  "state": "",
  "country": "",
  "postal_code": "",
  "project_image_url": "https://img.stokeleads.com/1751901395339-5rmlpv8lx7mfpeaq2c5v8i.jpeg",
  "consent": true,
  "source": "horizon_deck_builder",
  "location_id": "XIihUR3iXWQYFe7UPY6Z",
  "name": "<PERSON> Wen<PERSON>",
  "submitted_at": new Date().toISOString()
};

const webhookUrl = "https://services.leadconnectorhq.com/hooks/XIihUR3iXWQYFe7UPY6Z/webhook-trigger/MUYIKA9a4YvwYMYgiOXL";

console.log('Sending test data to:', webhookUrl);
console.log('Test data:', JSON.stringify(testData, null, 2));

fetch(webhookUrl, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(testData)
})
.then(response => {
  console.log('Response status:', response.status);
  console.log('Response headers:', Object.fromEntries(response.headers.entries()));
  return response.text();
})
.then(text => {
  console.log('Response body:', text);
  console.log('✅ Test webhook sent successfully!');
})
.catch(error => {
  console.error('❌ Error sending webhook:', error);
});
