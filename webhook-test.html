<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HighLevel Webhook Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            width: 100%;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HighLevel Webhook Test</h1>
        <p>Click the button below to send test data to your HighLevel webhook. This will help you map the payload in your automation.</p>
        
        <button onclick="sendTestData()">Send Test Data to HighLevel</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function sendTestData() {
            // UPDATE THIS URL FOR YOUR PROJECT
            const webhookUrl = "https://services.leadconnectorhq.com/hooks/YOUR_LOCATION_ID/webhook-trigger/YOUR_TRIGGER_ID";
            
            const testData = {
                "first_name": "Maria",
                "last_name": "Wendt",
                "phone": "5091234567",
                "email": "<EMAIL>",
                "address": "123 Main Street",
                "city": "Fairmount",
                "state": "GA",
                "country": "US",
                "postal_code": "30139",
                "project_image_url": "https://img.stokeleads.com/1751901395339-5rmlpv8lx7mfpeaq2c5v8i.jpeg",
                "consent": true,
                "source": "horizon_deck_builder", // UPDATE THIS
                "location_id": "YOUR_LOCATION_ID", // UPDATE THIS
                "name": "Maria Wendt",
                "submitted_at": new Date().toISOString(),
                "project_type": "Deck Building",
                "message": "I need a quote for a new deck construction"
            };

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Sending test data...</p>';

            try {
                console.log('Sending to:', webhookUrl);
                console.log('Data:', testData);

                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const responseText = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Success!</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong></p>
                        <pre>${responseText}</pre>
                        <p><strong>Data Sent:</strong></p>
                        <pre>${JSON.stringify(testData, null, 2)}</pre>
                    </div>
                `;

                console.log('Response status:', response.status);
                console.log('Response:', responseText);

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                        <p><strong>Data that was attempted to send:</strong></p>
                        <pre>${JSON.stringify(testData, null, 2)}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
