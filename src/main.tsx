import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Prevent FOUC by adding loaded class after DOM and styles are ready
const addLoadedClass = () => {
  document.documentElement.classList.add('loaded');
};

// Try multiple approaches to ensure styles are loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    // Wait for next frame to ensure layout is stable
    requestAnimationFrame(() => {
      requestAnimationFrame(addLoadedClass);
    });
  });
} else {
  // Document already loaded
  requestAnimationFrame(() => {
    requestAnimationFrame(addLoadedClass);
  });
}

// Fallback in case the above doesn't work
setTimeout(addLoadedClass, 50);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);